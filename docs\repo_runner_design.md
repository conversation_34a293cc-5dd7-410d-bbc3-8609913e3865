# Design Doc: Repo Runner - Intelligent GitHub Repository Analyzer

> Please DON'T remove notes for AI

## Requirements

> Notes for AI: Keep it simple and clear.
> If the requirements are abstract, write concrete user stories

### Core Requirements

The Repo Runner is an intelligent system that analyzes GitHub repositories to understand their structure and determine how to run applications in containers. The system should:

1. **Repository Crawling**: <PERSON><PERSON> and systematically explore GitHub repositories
2. **Intelligent File Analysis**: Understand file contents and their relationships
3. **Container Configuration Discovery**: Identify how to run the application in a container
4. **Adaptive Learning**: Continue reading files until sufficient information is gathered
5. **Decision Making**: Intelligently decide which files to read next based on current understanding

### User Stories

1. **As a DevOps engineer**, I want to automatically analyze a repository and get container deployment instructions without manual investigation
2. **As a developer**, I want to understand how to run any GitHub project locally in a container with minimal setup
3. **As a CI/CD system**, I want to automatically detect build and deployment configurations from repositories
4. **As a security auditor**, I want to understand the runtime requirements and dependencies of a repository

## Flow Design

> Notes for AI:
> 1. Consider the design patterns of agent, map-reduce, rag, and workflow. Apply them if they fit.
> 2. Present a concise, high-level description of the workflow.

### Applicable Design Patterns:

1. **Agentic Pattern**: Intelligent decision-making agent that determines which files to read next
2. **Map-Reduce Pattern**: Map individual file analysis, reduce to overall understanding
3. **RAG Pattern**: Retrieve relevant files based on current context, augment understanding
4. **Workflow Pattern**: Sequential steps with feedback loops for iterative learning

### Flow High-level Design:

1. **Repository Initialization Node**: Clone repository and perform initial scan
2. **File Priority Agent Node**: Intelligently prioritize which files to read next
3. **File Analysis Node**: Analyze selected files and extract relevant information
4. **Knowledge Synthesis Node**: Combine findings and assess completeness
5. **Container Strategy Node**: Generate container deployment strategy
6. **Validation Node**: Verify the proposed solution

```mermaid
flowchart TD
    A[Repository Initialization] --> B[File Priority Agent]
    B --> C[File Analysis]
    C --> D[Knowledge Synthesis]
    D --> E{Sufficient Info?}
    E -->|No| B
    E -->|Yes| F[Container Strategy]
    F --> G[Validation]
    G --> H{Strategy Valid?}
    H -->|No| B
    H -->|Yes| I[Output Results]
```

## Utility Functions

> Notes for AI:
> 1. Understand the utility function definition thoroughly by reviewing the doc.
> 2. Include only the necessary utility functions, based on nodes in the flow.

1. **Call LLM** (`utils/call_llm.py`)
   - *Input*: prompt (str)
   - *Output*: response (str)
   - Used by all analysis nodes for intelligent decision making

2. **GitHub API Client** (`utils/github_client.py`)
   - *Input*: repository_url (str), access_token (str)
   - *Output*: repository metadata and file tree
   - Used by Repository Initialization Node

3. **File Content Analyzer** (`utils/file_analyzer.py`)
   - *Input*: file_path (str), file_content (str)
   - *Output*: structured analysis (dict)
   - Used by File Analysis Node to extract metadata

4. **Container Strategy Generator** (`utils/container_strategy.py`)
   - *Input*: repository_analysis (dict)
   - *Output*: dockerfile and deployment instructions (dict)
   - Used by Container Strategy Node

5. **File Priority Scorer** (`utils/priority_scorer.py`)
   - *Input*: file_list (list), current_knowledge (dict)
   - *Output*: prioritized file list (list)
   - Used by File Priority Agent Node

## Node Design

### Shared Store

> Notes for AI: Try to minimize data redundancy

The shared store structure is organized as follows:

```python
shared = {
    "repository_url": "https://github.com/user/repo",
    "repository_metadata": {},
    "file_tree": [],
    "analyzed_files": {},
    "current_knowledge": {
        "language": None,
        "framework": None,
        "build_system": None,
        "dependencies": [],
        "entry_points": [],
        "configuration_files": [],
        "runtime_requirements": {}
    },
    "files_to_analyze": [],
    "container_strategy": {},
    "validation_results": {},
    "max_iterations": 50,
    "current_iteration": 0
}
```

### Node Steps

> Notes for AI: Carefully decide whether to use Batch/Async Node/Flow.

1. **Repository Initialization Node**
   - *Purpose*: Clone repository, scan file structure, and identify high-priority files
   - *Type*: Regular Node
   - *Steps*:
     - *prep*: Read "repository_url" from shared store
     - *exec*: Clone repository, build file tree, identify critical files (README, package.json, Dockerfile, etc.)
     - *post*: Write "repository_metadata", "file_tree", and initial "files_to_analyze" to shared store

2. **File Priority Agent Node**
   - *Purpose*: Intelligently decide which files to read next based on current knowledge gaps
   - *Type*: Regular Node
   - *Steps*:
     - *prep*: Read "current_knowledge", "files_to_analyze", and "analyzed_files" from shared store
     - *exec*: Use LLM to analyze knowledge gaps and prioritize remaining files
     - *post*: Update "files_to_analyze" with prioritized list

3. **File Analysis Node**
   - *Purpose*: Analyze selected files and extract relevant information for containerization
   - *Type*: Batch Node (process multiple files efficiently)
   - *Steps*:
     - *prep*: Read top N files from "files_to_analyze" and their content
     - *exec*: Extract language, dependencies, build instructions, runtime requirements
     - *post*: Update "analyzed_files" and "current_knowledge" with findings

4. **Knowledge Synthesis Node**
   - *Purpose*: Combine all findings and determine if enough information exists for containerization
   - *Type*: Regular Node
   - *Steps*:
     - *prep*: Read "current_knowledge" and "analyzed_files" from shared store
     - *exec*: Use LLM to assess completeness and identify remaining gaps
     - *post*: Update "current_knowledge" with synthesis and set completion flag

5. **Container Strategy Node**
   - *Purpose*: Generate Dockerfile and deployment instructions based on gathered knowledge
   - *Type*: Regular Node
   - *Steps*:
     - *prep*: Read complete "current_knowledge" from shared store
     - *exec*: Generate Dockerfile, docker-compose.yml, and deployment instructions
     - *post*: Write "container_strategy" to shared store

6. **Validation Node**
   - *Purpose*: Validate the proposed container strategy for completeness and correctness
   - *Type*: Regular Node
   - *Steps*:
     - *prep*: Read "container_strategy" and "current_knowledge" from shared store
     - *exec*: Use LLM to validate strategy and identify potential issues
     - *post*: Write "validation_results" and final recommendations to shared store

## Key Intelligence Features

### Adaptive File Selection

The File Priority Agent uses contextual understanding to decide which files to read next:

- **Language Detection Priority**: package.json → Python requirements → Go modules → Rust Cargo.toml
- **Framework Detection**: Look for framework-specific files after language is identified
- **Build System Discovery**: Makefile, build scripts, CI/CD configurations
- **Configuration Files**: Environment files, config directories, deployment manifests

### Knowledge Gap Analysis

The system continuously evaluates what information is missing:

- **Missing Dependencies**: Incomplete package lists, version requirements
- **Unknown Entry Points**: Main files, startup scripts, service definitions
- **Runtime Requirements**: Environment variables, external services, ports
- **Build Process**: Compilation steps, asset generation, test requirements

### Intelligent Stopping Criteria

The system knows when to stop crawling:

- **Sufficient Information**: All critical components identified for containerization
- **Diminishing Returns**: Recent files provide no new relevant information
- **Maximum Iterations**: Safety limit to prevent infinite crawling
- **Confidence Threshold**: High confidence in container strategy completeness

## Error Handling and Edge Cases

### Repository Access Issues
- Private repositories requiring authentication
- Large repositories with thousands of files
- Repositories with non-standard structures

### Analysis Challenges
- Multi-language repositories
- Microservice architectures
- Legacy codebases with minimal documentation
- Repositories with complex build processes

### Container Strategy Limitations
- Applications requiring specific hardware
- Services with complex external dependencies
- Applications with licensing restrictions
