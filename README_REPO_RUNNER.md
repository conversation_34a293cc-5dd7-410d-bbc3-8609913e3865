# Repo Runner - Intelligent Repository Containerization

Repo Runner is an intelligent system that analyzes GitHub repositories and automatically generates comprehensive containerization strategies. It uses the PocketFlow framework to orchestrate a sophisticated analysis pipeline that can handle incomplete repositories, fix common issues, and generate production-ready Docker configurations.

## Features

### 🔍 **Intelligent Analysis**
- **Adaptive File Selection**: Prioritizes files based on knowledge gaps and containerization needs
- **Multi-language Support**: Python, JavaScript/TypeScript, Go, Rust, Java, and more
- **Framework Detection**: Automatically detects Flask, Django, Express, Spring Boot, etc.
- **Dependency Analysis**: Extracts and analyzes project dependencies

### 🛠️ **Auto-Generation & Fixing**
- **Missing File Generation**: Creates requirements.txt, package.json, Dockerfile, docker-compose.yml
- **Code Issue Detection**: Identifies hardcoded ports, absolute paths, security vulnerabilities
- **Configuration Fixes**: Makes ports configurable, externalizes environment variables
- **Security Enhancements**: Moves secrets to environment variables, adds security best practices

### 🐳 **Container Strategy**
- **Optimized Dockerfiles**: Multi-stage builds, security best practices, minimal image sizes
- **Complete docker-compose**: Includes databases, caching, and all required services
- **Health Checks**: Automatic health check endpoint detection and configuration
- **Production Ready**: Non-root users, proper signal handling, resource optimization

### ✅ **Validation & Testing**
- **Build Validation**: Tests Docker builds and container startup
- **Configuration Validation**: Checks for common issues and misconfigurations
- **Performance Analysis**: Identifies optimization opportunities
- **Confidence Scoring**: Provides confidence metrics for the generated strategy

## Architecture

The system follows the enhanced design from `docs/repo_runner_design.md` and implements:

1. **Repository Initialization**: Clone and scan repository structure
2. **Intelligent File Analysis**: Adaptive file prioritization and analysis
3. **Knowledge Synthesis**: Combine findings and assess completeness
4. **Gap Detection**: Identify missing components and issues
5. **Code Analysis**: Extract containerization requirements from source code
6. **Auto-Generation**: Create missing files and fix issues
7. **Container Strategy**: Generate comprehensive deployment strategy
8. **Validation & Testing**: Validate and test the container configuration
9. **Optimization**: Suggest improvements and calculate confidence

## Installation

```bash
# Clone the repository
git clone <repository-url>
cd pocketflow

# Install dependencies
pip install -r requirements.txt

# Install additional dependencies for Repo Runner
pip install requests pyyaml
```

## Usage

### Command Line Interface

```bash
# Basic usage
python repo_runner_main.py https://github.com/user/repo

# With GitHub token for private repos
python repo_runner_main.py https://github.com/user/repo --token YOUR_TOKEN

# Save results to file
python repo_runner_main.py https://github.com/user/repo --output results.json

# Generate files to disk
python repo_runner_main.py https://github.com/user/repo --generate-files

# Verbose logging
python repo_runner_main.py https://github.com/user/repo --log-level DEBUG

# Custom iteration limit
python repo_runner_main.py https://github.com/user/repo --max-iterations 30
```

### Interactive Mode

```bash
# Run without arguments for interactive mode
python repo_runner_main.py
```

### Programmatic Usage

```python
from repo_runner_flow import run_repo_runner

# Analyze a repository
results = run_repo_runner(
    repository_url="https://github.com/user/repo",
    access_token="your_token",  # Optional
    max_iterations=50,
    log_level="INFO"
)

# Access results
print(f"Confidence: {results['confidence_score']}")
print(f"Language: {results['summary']['language']}")
print(f"Generated files: {list(results['generated_files'].keys())}")

# Get the Dockerfile
dockerfile = results['container_strategy']['dockerfile']
```

## File Structure

```
repo_runner/
├── utils/                          # Utility functions
│   ├── github_client.py           # GitHub API and repository operations
│   ├── file_analyzer.py           # File content analysis
│   ├── gap_detector.py            # Missing component detection
│   ├── code_analyzer.py           # Source code analysis
│   ├── auto_generator.py          # File generation and fixes
│   ├── container_validator.py     # Container validation and testing
│   ├── priority_scorer.py         # File prioritization logic
│   └── container_strategy.py      # Container strategy generation
├── nodes.py                       # Core analysis nodes
├── enhancement_nodes.py           # Enhancement and generation nodes
├── repo_runner_flow.py           # Flow orchestration
├── repo_runner_main.py           # Main CLI entry point
├── test_repo_runner.py           # Test suite
└── README_REPO_RUNNER.md         # This file
```

## Example Output

```
REPO RUNNER ANALYSIS RESULTS
================================================================================

Repository: user/example-flask-app
Files Analyzed: 12
Analysis Iterations: 3
Confidence Score: 0.87

Detected Language: python
Detected Framework: flask
Dependencies Found: 5
Ports Detected: [5000]
Environment Variables: 3

Files Generated: 4
  - requirements.txt
  - Dockerfile
  - docker-compose.yml
  - .env.example

Issues Found: 2
Critical Gaps: 0

Validation Status: PASSED
Validation Passed: Yes

Container Strategy Generated: Yes
Dockerfile Length: 1247 characters
Docker Compose: Generated

Recommendations (3):
  1. Consider using Alpine Linux base images for smaller size
  2. Add health check endpoint for container orchestration
  3. Use environment variables for database configuration
```

## Generated Files

### Dockerfile Example
```dockerfile
# Multi-stage build for Python application
FROM python:3.11-slim as builder

# Install build dependencies
RUN apt-get update && apt-get install -y gcc && rm -rf /var/lib/apt/lists/*

WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir --user -r requirements.txt

# Production stage
FROM python:3.11-slim

# Copy Python packages from builder
COPY --from=builder /root/.local /root/.local

WORKDIR /app
COPY . .

# Create non-root user
RUN useradd --create-home --shell /bin/bash appuser && chown -R appuser:appuser /app
USER appuser

ENV PATH=/root/.local/bin:$PATH
EXPOSE 5000

HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

CMD ["python", "app.py"]
```

### docker-compose.yml Example
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "5000:5000"
    environment:
      - DATABASE_URL=***************************************/app
      - PORT=5000
    depends_on:
      - postgres

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=app
      - POSTGRES_USER=app
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data:
```

## Testing

```bash
# Run the test suite
python test_repo_runner.py

# Test with a real repository (requires internet)
python repo_runner_main.py https://github.com/octocat/Hello-World --log-level DEBUG
```

## Configuration

### Environment Variables

- `GITHUB_TOKEN`: GitHub access token for private repositories
- `OPENAI_API_KEY`: OpenAI API key for LLM functionality (if using OpenAI)

### Customization

The system can be customized by modifying:

- **Priority scoring**: Edit `utils/priority_scorer.py` to change file prioritization
- **Gap detection**: Modify `utils/gap_detector.py` to add new gap types
- **File generation**: Extend `utils/auto_generator.py` with new file templates
- **Validation**: Add custom validation rules in `utils/container_validator.py`

## Limitations

- Requires internet access for repository cloning and LLM calls
- Docker validation requires Docker to be installed
- Large repositories may take longer to analyze
- Complex monorepos may need manual review of generated configurations

## Contributing

1. Follow the PocketFlow agentic coding methodology
2. Add tests for new functionality
3. Update documentation for new features
4. Ensure generated configurations follow container best practices

## License

This project is part of the PocketFlow framework and follows the same license terms.
