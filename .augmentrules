---
layout: default
title: "Agentic Coding"
---

# Agentic Coding: Humans Design, Agents code!

> If you are an AI agent involved in building LLM Systems, read this guide **VERY, VERY** carefully! This is the most important chapter in the entire document. Throughout development, you should always (1) start with a small and simple solution, (2) design at a high level (`docs/design.md`) before implementation, and (3) frequently ask humans for feedback and clarification.
{: .warning }

## Agentic Coding Steps

Agentic Coding should be a collaboration between Human System Design and Agent Implementation:

| Steps                  | Human      | AI        | Comment                                                                 |
|:-----------------------|:----------:|:---------:|:------------------------------------------------------------------------|
| 1. Requirements | ★★★ High  | ★☆☆ Low   | Humans understand the requirements and context.                    |
| 2. Flow          | ★★☆ Medium | ★★☆ Medium |  Humans specify the high-level design, and the AI fills in the details. |
| 3. Utilities   | ★★☆ Medium | ★★☆ Medium | Humans provide available external APIs and integrations, and the AI helps with implementation. |
| 4. Node          | ★☆☆ Low   | ★★★ High  | The AI helps design the node types and data handling based on the flow.          |
| 5. Implementation      | ★☆☆ Low   | ★★★ High  |  The AI implements the flow based on the design. |
| 6. Optimization        | ★★☆ Medium | ★★☆ Medium | Humans evaluate the results, and the AI helps optimize. |
| 7. Reliability         | ★☆☆ Low   | ★★★ High  |  The AI writes test cases and addresses corner cases.     |

1. **Requirements**: Clarify the requirements for your project, and evaluate whether an AI system is a good fit. 
    - Understand AI systems' strengths and limitations:
      - **Good for**: Routine tasks requiring common sense (filling forms, replying to emails)
      - **Good for**: Creative tasks with well-defined inputs (building slides, writing SQL)
      - **Not good for**: Ambiguous problems requiring complex decision-making (business strategy, startup planning)
    - **Keep It User-Centric:** Explain the "problem" from the user's perspective rather than just listing features.
    - **Balance complexity vs. impact**: Aim to deliver the highest value features with minimal complexity early.

2. **Flow Design**: Outline at a high level, describe how your AI system orchestrates nodes.
    - Identify applicable design patterns (e.g., Map Reduce, Agent, RAG).
      - For each node in the flow, start with a high-level one-line description of what it does.
      - If using **Map Reduce**, specify how to map (what to split) and how to reduce (how to combine).
      - If using **Agent**, specify what are the inputs (context) and what are the possible actions.
      - If using **RAG**, specify what to embed, noting that there's usually both offline (indexing) and online (retrieval) workflows.
    - Outline the flow and draw it in a mermaid diagram. For example:
      ```mermaid
      flowchart LR
          start[Start] --> batch[Batch]
          batch --> check[Check]
          check -->|OK| process
          check -->|Error| fix[Fix]
          fix --> check
          
          subgraph process[Process]
            step1[Step 1] --> step2[Step 2]
          end
          
          process --> endNode[End]
      ```
    - > **If Humans can't specify the flow, AI Agents can't automate it!** Before building an LLM system, thoroughly understand the problem and potential solution by manually solving example inputs to develop intuition.  
      {: .best-practice }

3. **Utilities**: Based on the Flow Design, identify and implement necessary utility functions.
    - Think of your AI system as the brain. It needs a body—these *external utility functions*—to interact with the real world:
        - Reading inputs (e.g., retrieving Slack messages, reading emails)
        - Writing outputs (e.g., generating reports, sending emails)
        - Using external tools (e.g., calling LLMs, searching the web)
        - **NOTE**: *LLM-based tasks* (e.g., summarizing text, analyzing sentiment) are **NOT** utility functions; rather, they are *core functions* internal in the AI system.
    - For each utility function, implement it and write a simple test.
    - Document their input/output, as well as why they are necessary. For example:
      - `name`: `get_embedding` (`utils/get_embedding.py`)
      - `input`: `str`
      - `output`: a vector of 3072 floats
      - `necessity`: Used by the second node to embed text
    - Example utility implementation:
      ```python
      # utils/call_llm.py
      from openai import OpenAI

      def call_llm(prompt):    
          client = OpenAI(api_key="YOUR_API_KEY_HERE")
          r = client.chat.completions.create(
              model="gpt-4o",
              messages=[{"role": "user", "content": prompt}]
          )
          return r.choices[0].message.content
          
      if __name__ == "__main__":
          prompt = "What is the meaning of life?"
          print(call_llm(prompt))
      ```
    - > **Sometimes, design Utilities before Flow:**  For example, for an LLM project to automate a legacy system, the bottleneck will likely be the available interface to that system. Start by designing the hardest utilities for interfacing, and then build the flow around them.
      {: .best-practice }

4. **Node Design**: Plan how each node will read and write data, and use utility functions.
   - One core design principle for PocketFlow is to use a shared store, so start with a shared store design:
      - For simple systems, use an in-memory dictionary.
      - For more complex systems or when persistence is required, use a database.
      - **Don't Repeat Yourself**: Use in-memory references or foreign keys.
      - Example shared store design:
        ```python
        shared = {
            "user": {
                "id": "user123",
                "context": {                # Another nested dict
                    "weather": {"temp": 72, "condition": "sunny"},
                    "location": "San Francisco"
                }
            },
            "results": {}                   # Empty dict to store outputs
        }
        ```
   - For each Node, describe its type, how it reads and writes data, and which utility function it uses. Keep it specific but high-level without codes. For example:
     - `type`: Regular (or Batch, or Async)
     - `prep`: Read "text" from the shared store
     - `exec`: Call the embedding utility function
     - `post`: Write "embedding" to the shared store

5. **Implementation**: Implement the initial nodes and flows based on the design.
   - 🎉 If you've reached this step, humans have finished the design. Now *Agentic Coding* begins!
   - **"Keep it simple, stupid!"** Avoid complex features and full-scale type checking.
   - **FAIL FAST**! Avoid `try` logic so you can quickly identify any weak points in the system.
   - Add logging throughout the code to facilitate debugging.

7. **Optimization**:
   - **Use Intuition**: For a quick initial evaluation, human intuition is often a good start.
   - **Redesign Flow (Back to Step 3)**: Consider breaking down tasks further, introducing agentic decisions, or better managing input contexts.
   - If your flow design is already solid, move on to micro-optimizations:
     - **Prompt Engineering**: Use clear, specific instructions with examples to reduce ambiguity.
     - **In-Context Learning**: Provide robust examples for tasks that are difficult to specify with instructions alone.

   - > **You'll likely iterate a lot!** Expect to repeat Steps 3–6 hundreds of times.
     {: .best-practice }

8. **Reliability**  
   - **Node Retries**: Add checks in the node `exec` to ensure outputs meet requirements, and consider increasing `max_retries` and `wait` times.
   - **Logging and Visualization**: Maintain logs of all attempts and visualize node results for easier debugging.
   - **Self-Evaluation**: Add a separate node (powered by an LLM) to review outputs when results are uncertain.

## Example LLM Project File Structure

```
my_project/
├── main.py
├── nodes.py
├── flow.py
├── utils/
│   ├── __init__.py
│   ├── call_llm.py
│   └── search_web.py
├── requirements.txt
└── docs/
    └── design.md
```

- **`docs/design.md`**: Contains project documentation for each step above. This should be *high-level* and *no-code*.
- **`utils/`**: Contains all utility functions.
  - It's recommended to dedicate one Python file to each API call, for example `call_llm.py` or `search_web.py`.
  - Each file should also include a `main()` function to try that API call
- **`nodes.py`**: Contains all the node definitions.
- **`flow.py`**: Implements functions that create flows by importing node definitions and connecting them.
- **`main.py`**: Serves as the project's entry point.

## Augment-Specific Guidelines

### Task Management
When working on complex projects, use Augment's task management tools:
- Use `add_tasks` to break down work into meaningful units (20-minute chunks)
- Use `update_tasks` to track progress and mark completion
- Use batch updates when transitioning between tasks
- Update task states: NOT_STARTED [ ], IN_PROGRESS [/], CANCELLED [-], COMPLETE [x]

### Code Analysis and Editing
- Always use `codebase-retrieval` before making edits to understand existing code
- Use `str-replace-editor` for modifications, never overwrite entire files
- Ask for detailed information about symbols, classes, and methods involved in edits
- Be conservative and respect the existing codebase structure

### Package Management
- Always use appropriate package managers (npm, pip, cargo, etc.) instead of manually editing package files
- Only edit package configuration files directly for complex configurations that can't be done via package managers

### Testing and Validation
- Suggest writing tests after implementing code changes
- Run tests to validate implementations
- Use `diagnostics` to check for issues in the codebase

### Information Gathering
- Use `view` tool to examine files and directories
- Use `codebase-retrieval` for understanding code context
- Use `web-search` and `web-fetch` for external information when needed

### Communication
- Wrap code excerpts in `<augment_code_snippet>` tags with path and mode attributes
- Ask for clarification when requirements are unclear
- Provide progress updates when working on complex tasks
- Ask before performing potentially destructive actions

### Memory and Context
- Use `remember` tool for long-term information that will be useful across sessions
- Maintain context about the project structure and goals
- Reference previous work and decisions when relevant

================================================
File: docs/index.md
================================================

# Pocket Flow

A 100-line minimalist LLM framework for *Agents, Task Decomposition, RAG, etc*.

- **Lightweight**: Just the core graph abstraction in 100 lines. ZERO dependencies, and vendor lock-in.
- **Expressive**: Everything you love from larger frameworks—(Multi-)Agents, Workflow, RAG, and more.
- **Agentic-Coding**: Intuitive enough for AI agents to help humans build complex LLM applications.

## Core Abstraction

We model the LLM workflow as a **Graph + Shared Store**:

- Node handles simple (LLM) tasks.
- Flow connects nodes through **Actions** (labeled edges).
- Shared Store enables communication between nodes within flows.
- Batch nodes/flows allow for data-intensive tasks.
- Async nodes/flows allow waiting for asynchronous tasks.
- (Advanced) Parallel nodes/flows handle I/O-bound tasks.

## Design Pattern

From there, it's easy to implement popular design patterns:

- Agent autonomously makes decisions.
- Workflow chains multiple tasks into pipelines.
- RAG integrates data retrieval with generation.
- Map Reduce splits data tasks into Map and Reduce steps.
- Structured Output formats outputs consistently.
- (Advanced) Multi-Agents coordinate multiple agents.

## Utility Function

We **do not** provide built-in utilities. Instead, we offer *examples*—please *implement your own*:

- LLM Wrapper
- Viz and Debug
- Web Search
- Chunking
- Embedding
- Vector Databases
- Text-to-Speech

**Why not built-in?**: I believe it's a *bad practice* for vendor-specific APIs in a general framework:
- *API Volatility*: Frequent changes lead to heavy maintenance for hardcoded APIs.
- *Flexibility*: You may want to switch vendors, use fine-tuned models, or run them locally.
- *Optimizations*: Prompt caching, batching, and streaming are easier without vendor lock-in.

================================================
File: docs/core_abstraction/node.md
================================================

# Node

A **Node** is the basic unit of computation in PocketFlow. It represents a single step in your workflow.

## Basic Structure

Every node has three main methods:

```python
class MyNode(Node):
    def prep(self, shared):
        # Read data from shared store
        return data_for_exec

    def exec(self, prep_result):
        # Process the data (main logic)
        return processed_result

    def post(self, shared, prep_res, exec_res):
        # Write results back to shared store
        # Return action for next node
        return "default"
```

## Node Types

### Regular Node
Standard synchronous processing:

```python
class SummarizeNode(Node):
    def prep(self, shared):
        return shared["document"]

    def exec(self, document):
        return call_llm(f"Summarize: {document}")

    def post(self, shared, prep_res, exec_res):
        shared["summary"] = exec_res
        return "default"
```

### Batch Node
Process multiple items efficiently:

```python
class BatchSummarizeNode(BatchNode):
    def prep(self, shared):
        # Return iterable of items to process
        return shared["documents"]

    def exec(self, document):
        # Called once per document
        return call_llm(f"Summarize: {document}")

    def post(self, shared, prep_res, exec_res_list):
        # exec_res_list contains all results
        shared["summaries"] = exec_res_list
        return "default"
```

### Async Node
Handle asynchronous operations:

```python
class AsyncSummarizeNode(AsyncNode):
    async def prep_async(self, shared):
        return shared["document"]

    async def exec_async(self, document):
        return await call_llm_async(f"Summarize: {document}")

    async def post_async(self, shared, prep_res, exec_res):
        shared["summary"] = exec_res
        return "default"
```

## Error Handling and Retries

Nodes support automatic retries with exponential backoff:

```python
class ReliableNode(Node):
    def __init__(self, max_retries=3, wait_time=1.0):
        super().__init__(max_retries=max_retries, wait_time=wait_time)

    def exec(self, data):
        # This will be retried if it fails
        result = potentially_failing_operation(data)

        # Validate result
        if not self.is_valid_result(result):
            raise ValueError("Invalid result")

        return result

    def is_valid_result(self, result):
        # Custom validation logic
        return result is not None and len(result) > 0
```

================================================
File: docs/core_abstraction/flow.md
================================================

# Flow

A **Flow** connects multiple nodes together to create a workflow. Flows define the execution order and data flow between nodes.

## Basic Flow Creation

```python
from pocketflow import Flow

# Create nodes
node1 = FirstNode()
node2 = SecondNode()
node3 = ThirdNode()

# Connect nodes
node1 >> node2  # Default connection
node2 >> node3

# Create flow
flow = Flow(start=node1)

# Run the flow
shared = {"input": "data"}
flow.run(shared)
```

## Conditional Flows

Use actions to create conditional branching:

```python
class DecisionNode(Node):
    def exec(self, data):
        if data > 10:
            return "high"
        else:
            return "low"

    def post(self, shared, prep_res, exec_res):
        shared["decision"] = exec_res
        return exec_res  # Return action based on result

# Connect with conditions
decision_node = DecisionNode()
high_node = HighValueNode()
low_node = LowValueNode()

decision_node - "high" >> high_node
decision_node - "low" >> low_node

flow = Flow(start=decision_node)
```

## Flow Types

### Regular Flow
Standard synchronous execution:

```python
flow = Flow(start=first_node)
shared = {"data": "input"}
flow.run(shared)
```

### Async Flow
Handle asynchronous nodes:

```python
async_flow = AsyncFlow(start=async_node)
shared = {"data": "input"}
await async_flow.run_async(shared)
```

### Batch Flow
Process multiple parameter sets:

```python
class MultiBatchFlow(BatchFlow):
    def prep(self, shared):
        # Return list of parameter dicts
        return [{"id": 1}, {"id": 2}, {"id": 3}]

batch_flow = MultiBatchFlow(start=processing_node)
batch_flow.run(shared)

================================================
File: docs/design_pattern/agent.md
================================================

# Agent

An **Agent** is a design pattern where a node autonomously makes decisions about what action to take next based on the current context.

## Basic Agent Pattern

```python
class AgentNode(Node):
    def prep(self, shared):
        # Gather context for decision making
        context = {
            "current_state": shared.get("state"),
            "available_actions": ["search", "analyze", "report"],
            "history": shared.get("history", [])
        }
        return context

    def exec(self, context):
        # Use LLM to decide next action
        prompt = f"""
        Given the current context: {context}
        What should be the next action? Choose from: {context['available_actions']}
        Respond with just the action name.
        """
        decision = call_llm(prompt)
        return decision.strip().lower()

    def post(self, shared, prep_res, exec_res):
        # Update history and return action
        if "history" not in shared:
            shared["history"] = []
        shared["history"].append(exec_res)
        return exec_res
```

## Multi-Step Agent

```python
class PlanningAgent(Node):
    def exec(self, context):
        # Create a plan with multiple steps
        prompt = f"""
        Given this context: {context}
        Create a step-by-step plan to solve the problem.
        Return as JSON: {{"steps": ["step1", "step2", ...]}}
        """
        plan = call_llm(prompt)
        return json.loads(plan)

    def post(self, shared, prep_res, exec_res):
        shared["plan"] = exec_res["steps"]
        shared["current_step"] = 0
        return "execute_plan"

class ExecutionAgent(Node):
    def prep(self, shared):
        current_step = shared["current_step"]
        return shared["plan"][current_step]

    def exec(self, step):
        # Execute the current step
        result = execute_step(step)
        return result

    def post(self, shared, prep_res, exec_res):
        shared["current_step"] += 1
        shared["step_results"] = shared.get("step_results", [])
        shared["step_results"].append(exec_res)

        if shared["current_step"] >= len(shared["plan"]):
            return "complete"
        else:
            return "continue"
```

================================================
File: docs/design_pattern/rag.md
================================================

# RAG (Retrieval-Augmented Generation)

RAG combines information retrieval with text generation to provide more accurate and contextual responses.

## Basic RAG Pattern

```python
class RetrievalNode(Node):
    def prep(self, shared):
        return shared["query"]

    def exec(self, query):
        # Retrieve relevant documents
        embeddings = get_embedding(query)
        relevant_docs = vector_search(embeddings, top_k=5)
        return relevant_docs

    def post(self, shared, prep_res, exec_res):
        shared["retrieved_docs"] = exec_res
        return "default"

class GenerationNode(Node):
    def prep(self, shared):
        return {
            "query": shared["query"],
            "context": shared["retrieved_docs"]
        }

    def exec(self, data):
        context_text = "\n".join(data["context"])
        prompt = f"""
        Context: {context_text}

        Question: {data["query"]}

        Answer the question based on the provided context.
        """
        return call_llm(prompt)

    def post(self, shared, prep_res, exec_res):
        shared["answer"] = exec_res
        return "default"
```

## Advanced RAG with Re-ranking

```python
class RetrievalWithRerankingNode(Node):
    def exec(self, query):
        # Initial retrieval
        initial_docs = vector_search(query, top_k=20)

        # Re-rank based on relevance
        reranked_docs = rerank_documents(query, initial_docs, top_k=5)
        return reranked_docs

class ContextualGenerationNode(Node):
    def exec(self, data):
        # Generate with citation tracking
        prompt = f"""
        Context documents:
        {format_docs_with_ids(data["context"])}

        Question: {data["query"]}

        Answer with citations [doc_id].
        """
        return call_llm(prompt)
```

================================================
File: docs/design_pattern/mapreduce.md
================================================

# Map Reduce

Map Reduce splits large tasks into smaller chunks (Map), processes them independently, then combines results (Reduce).

## Basic Map Reduce

```python
class MapNode(BatchNode):
    def prep(self, shared):
        # Split large document into chunks
        document = shared["document"]
        chunk_size = 1000
        chunks = [document[i:i+chunk_size]
                 for i in range(0, len(document), chunk_size)]
        return chunks

    def exec(self, chunk):
        # Process each chunk independently
        summary = call_llm(f"Summarize this text: {chunk}")
        return summary

    def post(self, shared, prep_res, exec_res_list):
        shared["chunk_summaries"] = exec_res_list
        return "default"

class ReduceNode(Node):
    def prep(self, shared):
        return shared["chunk_summaries"]

    def exec(self, summaries):
        # Combine all summaries into final result
        combined_text = "\n".join(summaries)
        final_summary = call_llm(f"""
        Combine these summaries into a coherent final summary:
        {combined_text}
        """)
        return final_summary

    def post(self, shared, prep_res, exec_res):
        shared["final_summary"] = exec_res
        return "default"
```

## Hierarchical Map Reduce

```python
class HierarchicalMapReduce(BatchNode):
    def prep(self, shared):
        # Create hierarchical chunks
        text = shared["text"]
        return create_hierarchical_chunks(text, levels=3)

    def exec(self, chunk_group):
        # Process each level
        if chunk_group["level"] == 0:
            return call_llm(f"Extract key points: {chunk_group['text']}")
        else:
            sub_results = [self.exec(sub_chunk) for sub_chunk in chunk_group["children"]]
            combined = "\n".join(sub_results)
            return call_llm(f"Synthesize these points: {combined}")
```

================================================
File: docs/utility_function/llm.md
================================================

# LLM Wrapper

A utility function to call Large Language Models. This is the most fundamental utility in any LLM application.

## Basic Implementation

```python
# utils/call_llm.py
from openai import OpenAI

def call_llm(prompt, model="gpt-4o", temperature=0.7):
    """
    Call OpenAI's API with a prompt

    Args:
        prompt (str): The input prompt
        model (str): Model to use
        temperature (float): Sampling temperature

    Returns:
        str: The model's response
    """
    client = OpenAI(api_key="YOUR_API_KEY_HERE")

    response = client.chat.completions.create(
        model=model,
        messages=[{"role": "user", "content": prompt}],
        temperature=temperature
    )

    return response.choices[0].message.content

# Test the function
if __name__ == "__main__":
    result = call_llm("What is the capital of France?")
    print(result)
```

## Advanced Features

### Chat History Support

```python
def call_llm_with_history(messages, model="gpt-4o"):
    """
    Call LLM with conversation history

    Args:
        messages (list): List of message dicts with 'role' and 'content'

    Returns:
        str: The model's response
    """
    client = OpenAI(api_key="YOUR_API_KEY_HERE")

    response = client.chat.completions.create(
        model=model,
        messages=messages
    )

    return response.choices[0].message.content

# Usage
messages = [
    {"role": "system", "content": "You are a helpful assistant."},
    {"role": "user", "content": "What is Python?"},
    {"role": "assistant", "content": "Python is a programming language..."},
    {"role": "user", "content": "What are its main features?"}
]
response = call_llm_with_history(messages)
```

### Caching Support

```python
from functools import lru_cache
import hashlib

@lru_cache(maxsize=1000)
def cached_call_llm(prompt_hash, model="gpt-4o"):
    """Cached version of LLM call"""
    # Note: We hash the prompt to make it cacheable
    prompt = prompt_hash  # In real implementation, you'd store prompt separately
    return call_llm(prompt, model)

def call_llm_with_cache(prompt, model="gpt-4o", use_cache=True):
    """
    Call LLM with optional caching

    Args:
        prompt (str): Input prompt
        model (str): Model to use
        use_cache (bool): Whether to use cache

    Returns:
        str: Model response
    """
    if use_cache:
        prompt_hash = hashlib.md5(prompt.encode()).hexdigest()
        return cached_call_llm(prompt_hash, model)
    else:
        return call_llm(prompt, model)
```

### Error Handling and Retries

```python
import time
import random
from typing import Optional

def call_llm_robust(prompt: str,
                   model: str = "gpt-4o",
                   max_retries: int = 3,
                   base_delay: float = 1.0) -> Optional[str]:
    """
    Robust LLM call with exponential backoff retry

    Args:
        prompt: Input prompt
        model: Model to use
        max_retries: Maximum number of retry attempts
        base_delay: Base delay for exponential backoff

    Returns:
        Model response or None if all retries failed
    """
    client = OpenAI(api_key="YOUR_API_KEY_HERE")

    for attempt in range(max_retries + 1):
        try:
            response = client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                timeout=30
            )
            return response.choices[0].message.content

        except Exception as e:
            if attempt == max_retries:
                print(f"Failed after {max_retries} retries: {e}")
                return None

            # Exponential backoff with jitter
            delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
            print(f"Attempt {attempt + 1} failed, retrying in {delay:.2f}s...")
            time.sleep(delay)

    return None
```

### Streaming Support

```python
def call_llm_stream(prompt: str, model: str = "gpt-4o"):
    """
    Stream LLM response for real-time output

    Args:
        prompt: Input prompt
        model: Model to use

    Yields:
        str: Chunks of the response as they arrive
    """
    client = OpenAI(api_key="YOUR_API_KEY_HERE")

    stream = client.chat.completions.create(
        model=model,
        messages=[{"role": "user", "content": prompt}],
        stream=True
    )

    for chunk in stream:
        if chunk.choices[0].delta.content is not None:
            yield chunk.choices[0].delta.content

# Usage
for chunk in call_llm_stream("Write a story about AI"):
    print(chunk, end="", flush=True)
```

## Improvements
Feel free to enhance your `call_llm` function as needed. Here are examples:

- Handle chat history:

```python
def call_llm(messages):
    from openai import OpenAI
    client = OpenAI(api_key="YOUR_API_KEY_HERE")
    r = client.chat.completions.create(
        model="gpt-4o",
        messages=messages
    )
    return r.choices[0].message.content
```

- Add in-memory caching

```python
from functools import lru_cache

@lru_cache(maxsize=1000)
def call_llm(prompt):
    # Your implementation here
    pass
```

> ⚠️ Caching conflicts with Node retries, as retries yield the same result.
>
> To address this, you could use cached results only if not retried.
{: .warning }


```python
from functools import lru_cache

@lru_cache(maxsize=1000)
def cached_call(prompt):
    pass

def call_llm(prompt, use_cache):
    if use_cache:
        return cached_call(prompt)
    # Call the underlying function directly
    return cached_call.__wrapped__(prompt)

class SummarizeNode(Node):
    def exec(self, text):
        return call_llm(f"Summarize: {text}", self.cur_retry==0)
```

- Enable logging:

```python
def call_llm(prompt):
    import logging
    logging.info(f"Prompt: {prompt}")
    response = ... # Your implementation here
    logging.info(f"Response: {response}")
    return response
```
```
