# Repo Runner Requirements
# Core dependencies for the Repo Runner system

# Google Generative AI for LLM functionality
google-generativeai>=0.3.0

# HTTP requests for GitHub API
requests>=2.25.0

# YAML parsing for configuration files
PyYAML>=6.0

# AST parsing for Python code analysis
# (built-in, no additional requirement)

# JSON handling
# (built-in, no additional requirement)

# Regular expressions
# (built-in, no additional requirement)

# File operations and path handling
# (built-in, no additional requirement)

# Subprocess for Docker operations
# (built-in, no additional requirement)

# Temporary file operations
# (built-in, no additional requirement)

# Logging
# (built-in, no additional requirement)

# Optional: For enhanced Docker validation
# docker>=6.0.0  # Uncomment if you want Python Docker SDK

# Optional: For enhanced security scanning
# safety>=2.0.0  # Uncomment for dependency vulnerability scanning

# Optional: For enhanced code analysis
# bandit>=1.7.0  # Uncomment for security linting
# pylint>=2.15.0  # Uncomment for code quality analysis

# Development dependencies (optional)
# pytest>=7.0.0  # For running tests
# black>=22.0.0  # For code formatting
# flake8>=5.0.0  # For linting
